import 'package:flutter/material.dart';
import 'core/theme/app_theme.dart';
import 'presentation/screens/wishlist_screen.dart';

/// Demo app to showcase the Wishlist Screen
/// 
/// This is a standalone demo that shows the wishlist screen implementation
/// based on the HTML design provided in temp.txt
/// 
/// Features:
/// - Responsive design that works on mobile and tablet
/// - Clean white background as preferred
/// - Product cards with images, ratings, and prices
/// - Remove from wishlist functionality
/// - Add to cart functionality
/// - Empty state when no items
/// - Smooth animations and interactions
/// 
/// To run this demo:
/// 1. Replace the main.dart content with this file's content
/// 2. Run: flutter run
/// 
/// Or use this as a reference for integrating the wishlist screen
/// into your existing app navigation.

void main() {
  runApp(const WishlistDemo());
}

class WishlistDemo extends StatelessWidget {
  const WishlistDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wishlist Demo',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const WishlistScreen(),
    );
  }
}

/// Integration Example:
/// 
/// To integrate the wishlist screen into your existing app:
/// 
/// 1. Import the wishlist screen:
///    ```dart
///    import 'presentation/screens/wishlist_screen.dart';
///    ```
/// 
/// 2. Navigate to it from any button or navigation:
///    ```dart
///    Navigator.push(
///      context,
///      MaterialPageRoute(builder: (context) => const WishlistScreen()),
///    );
///    ```
/// 
/// 3. For bottom navigation integration:
///    ```dart
///    BottomNavigationBarItem(
///      icon: Icon(Icons.favorite_border),
///      activeIcon: Icon(Icons.favorite),
///      label: 'Wishlist',
///    )
///    ```
/// 
/// 4. For app bar integration:
///    ```dart
///    IconButton(
///      icon: Icon(Icons.favorite_border),
///      onPressed: () => Navigator.push(
///        context,
///        MaterialPageRoute(builder: (context) => const WishlistScreen()),
///      ),
///    )
///    ```
/// 
/// The wishlist screen includes:
/// - Responsive design (mobile/tablet)
/// - Clean white background
/// - Product cards with all details
/// - Interactive buttons with animations
/// - Empty state handling
/// - Snackbar notifications
/// - Material Design 3 styling
/// 
/// All styling follows the existing app theme and color scheme
/// defined in AppColors and AppConstants.
