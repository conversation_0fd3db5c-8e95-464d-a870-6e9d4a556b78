<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Profile Screen - Shop App</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>
tailwind.config={
darkMode: 'class',
theme:{
extend:{
colors:{
primary:'#FF4E50',
secondary:'#FC913A',
dark: {
bg: '#121212',
card: '#1E1E1E',
text: '#E5E5E5'
}
},
borderRadius:{
'none':'0px',
'sm':'4px',
DEFAULT:'8px',
'md':'12px',
'lg':'16px',
'xl':'20px',
'2xl':'24px',
'3xl':'32px',
'full':'9999px',
'button':'8px'
}
}
}
}
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
body {
font-family: 'Inter', sans-serif;
max-width: 375px;
margin: 0 auto;
min-height: 100vh;
position: relative;
background-color: #f9f9f9;
}
.profile-action:hover {
background-color: #f8f9fa;
transform: translateY(-1px);
}
.profile-action {
transition: all 0.2s ease;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Mobile App Container -->
<div class="max-w-md mx-auto bg-gray-50 min-h-screen relative">
<!-- Header -->
<div class="bg-white px-4 py-3 flex items-center justify-between border-b border-gray-100">
<button class="p-2 -ml-2" onclick="history.back()">
<svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
</svg>
</button>
<h1 class="text-lg font-semibold text-gray-900">Profile</h1>
<button class="p-2 -mr-2">
<svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
</svg>
</button>
</div>

<!-- Profile Header -->
<div class="px-4 py-6 bg-white">
<div class="flex items-center">
<div class="relative">
<div class="w-20 h-20 rounded-full bg-gray-100 overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%20profile%20photo%20of%20a%20person%2C%20natural%20lighting%2C%20clean%20background%2C%20high%20quality%2C%20detailed%20headshot&width=80&height=80&seq=40&orientation=squarish" alt="Profile Photo" class="w-full h-full object-cover">
</div>
<button class="absolute bottom-0 right-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white shadow-md">
<i class="ri-camera-line"></i>
</button>
</div>
<div class="ml-4 flex-1">
<h2 class="text-lg font-semibold">Sarah Anderson</h2>
<p class="text-sm text-gray-500"><EMAIL></p>
<div class="flex items-center mt-1">
<i class="ri-verified-badge-fill text-primary"></i>
<span class="text-xs text-primary ml-1">Verified Account</span>
</div>
</div>
</div>
<div class="flex justify-between mt-6">
<div class="text-center flex-1">
<p class="text-sm font-medium">Orders</p>
<p class="text-lg font-semibold mt-1">12</p>
</div>
<div class="text-center flex-1 border-l border-r">
<p class="text-sm font-medium">Wishlist</p>
<p class="text-lg font-semibold mt-1">24</p>
</div>
<div class="text-center flex-1">
<p class="text-sm font-medium">Reviews</p>
<p class="text-lg font-semibold mt-1">8</p>
</div>
</div>
</div>

<!-- Profile Actions -->
<div class="px-4 py-4">
<div class="space-y-3">
<a href="#" class="profile-action bg-white rounded-lg p-4 flex items-center justify-between">
<div class="flex items-center">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-user-settings-line text-primary ri-lg"></i>
</div>
<span class="ml-3 text-sm font-medium">Account Settings</span>
</div>
<i class="ri-arrow-right-s-line text-gray-400"></i>
</a>
<a href="#" class="profile-action bg-white rounded-lg p-4 flex items-center justify-between">
<div class="flex items-center">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-map-pin-line text-primary ri-lg"></i>
</div>
<span class="ml-3 text-sm font-medium">Shipping Addresses</span>
</div>
<i class="ri-arrow-right-s-line text-gray-400"></i>
</a>
<a href="#" class="profile-action bg-white rounded-lg p-4 flex items-center justify-between">
<div class="flex items-center">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-bank-card-line text-primary ri-lg"></i>
</div>
<span class="ml-3 text-sm font-medium">Payment Methods</span>
</div>
<i class="ri-arrow-right-s-line text-gray-400"></i>
</a>
<a href="#" class="profile-action bg-white rounded-lg p-4 flex items-center justify-between">
<div class="flex items-center">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-history-line text-primary ri-lg"></i>
</div>
<span class="ml-3 text-sm font-medium">Order History</span>
</div>
<i class="ri-arrow-right-s-line text-gray-400"></i>
</a>
<a href="#" class="profile-action bg-white rounded-lg p-4 flex items-center justify-between">
<div class="flex items-center">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-chat-1-line text-primary ri-lg"></i>
</div>
<span class="ml-3 text-sm font-medium">Help & Support</span>
</div>
<i class="ri-arrow-right-s-line text-gray-400"></i>
</a>
<a href="#" class="profile-action bg-white rounded-lg p-4 flex items-center justify-between">
<div class="flex items-center">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-settings-3-line text-primary ri-lg"></i>
</div>
<span class="ml-3 text-sm font-medium">App Settings</span>
</div>
<i class="ri-arrow-right-s-line text-gray-400"></i>
</a>
</div>
</div>

<!-- Sign Out Button -->
<div class="px-4 mt-4">
<button class="w-full py-3.5 border border-gray-200 text-gray-600 font-medium rounded-button flex items-center justify-center space-x-2 hover:bg-gray-50 transition-colors">
<i class="ri-logout-circle-line"></i>
<span>Sign Out</span>
</button>
</div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
// Profile action handlers
const profileActions = document.querySelectorAll('.profile-action');
profileActions.forEach(action => {
action.addEventListener('click', function(e) {
e.preventDefault();
const actionText = this.querySelector('span').textContent;
alert(`Navigate to: ${actionText}`);
});
});

// Sign out handler
const signOutBtn = document.querySelector('button[class*="Sign Out"]');
if (signOutBtn) {
signOutBtn.addEventListener('click', function() {
if (confirm('Are you sure you want to sign out?')) {
alert('Signing out...');
}
});
}
});
</script>
</body>
</html>
