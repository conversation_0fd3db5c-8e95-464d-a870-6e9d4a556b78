class User {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;
  final bool isVerified;
  final int ordersCount;
  final int wishlistCount;
  final int reviewsCount;
  final DateTime? lastLoginAt;
  final DateTime createdAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
    this.isVerified = false,
    this.ordersCount = 0,
    this.wishlistCount = 0,
    this.reviewsCount = 0,
    this.lastLoginAt,
    required this.createdAt,
  });

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? profileImageUrl,
    bool? isVerified,
    int? ordersCount,
    int? wishlistCount,
    int? reviewsCount,
    DateTime? lastLoginAt,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isVerified: isVerified ?? this.isVerified,
      ordersCount: ordersCount ?? this.ordersCount,
      wishlistCount: wishlistCount ?? this.wishlistCount,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return 'U';
  }

  bool get hasProfileImage =>
      profileImageUrl != null && profileImageUrl!.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.profileImageUrl == profileImageUrl &&
        other.isVerified == isVerified &&
        other.ordersCount == ordersCount &&
        other.wishlistCount == wishlistCount &&
        other.reviewsCount == reviewsCount;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        profileImageUrl.hashCode ^
        isVerified.hashCode ^
        ordersCount.hashCode ^
        wishlistCount.hashCode ^
        reviewsCount.hashCode;
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, isVerified: $isVerified)';
  }
}

// Sample user data for demo purposes
class UserRepository {
  static User getCurrentUser() {
    return User(
      id: '1',
      name: 'Sarah Anderson',
      email: '<EMAIL>',
      profileImageUrl:
          'https://readdy.ai/api/search-image?query=professional%20profile%20photo%20of%20a%20person%2C%20natural%20lighting%2C%20clean%20background%2C%20high%20quality%2C%20detailed%20headshot&width=80&height=80&seq=40&orientation=squarish',
      isVerified: true,
      ordersCount: 12,
      wishlistCount: 24,
      reviewsCount: 8,
      createdAt: DateTime(2023, 1, 15),
    );
  }
}
