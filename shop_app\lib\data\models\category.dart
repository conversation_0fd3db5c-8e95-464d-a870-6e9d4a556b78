import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';

class Category {
  final String id;
  final String name;
  final String iconUrl;
  final Color backgroundColor;
  final int productCount;
  final String? description;
  final List<String>? subcategories;

  Category({
    required this.id,
    required this.name,
    required this.iconUrl,
    required this.backgroundColor,
    this.productCount = 0,
    this.description,
    this.subcategories,
  });
}

class CategoryData {
  static List<Category> getCategories() {
    return [
      Category(
        id: 'clothing',
        name: 'Clothing',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20clothing%20and%20fashion%20items%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=2&orientation=squarish',
        backgroundColor: AppColors.categoryClothing.withValues(alpha: 0.1),
        productCount: 156,
      ),
      Category(
        id: 'electronics',
        name: 'Electronics',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20electronic%20devices%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=3&orientation=squarish',
        backgroundColor: AppColors.categoryElectronics.withValues(alpha: 0.1),
        productCount: 89,
      ),
      Category(
        id: 'home',
        name: 'Home',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20home%20decor%20items%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=4&orientation=squarish',
        backgroundColor: AppColors.categoryHome.withValues(alpha: 0.1),
        productCount: 234,
      ),
      Category(
        id: 'beauty',
        name: 'Beauty',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20beauty%20products%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=5&orientation=squarish',
        backgroundColor: AppColors.categoryBeauty.withValues(alpha: 0.1),
        productCount: 67,
      ),
      Category(
        id: 'sports',
        name: 'Sports',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20sports%20equipment%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=6&orientation=squarish',
        backgroundColor: AppColors.categorySports.withValues(alpha: 0.1),
        productCount: 123,
      ),
      Category(
        id: 'toys',
        name: 'Toys',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20toys%20and%20games%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=7&orientation=squarish',
        backgroundColor: AppColors.categoryToys.withValues(alpha: 0.1),
        productCount: 45,
      ),
    ];
  }

  static List<Category> getDetailedCategories() {
    return [
      Category(
        id: 'electronics',
        name: 'Electronics',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20electronics%20and%20gadgets%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=40&height=40&seq=23&orientation=squarish',
        backgroundColor: AppColors.categoryElectronics.withValues(alpha: 0.1),
        productCount: 2156,
        description: 'Gadgets & Accessories',
        subcategories: [
          'Smartphones',
          'Laptops',
          'Headphones',
          'Smartwatches',
          'Cameras',
          'Accessories',
        ],
      ),
      Category(
        id: 'fashion',
        name: 'Fashion',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20fashion%20and%20clothing%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=40&height=40&seq=24&orientation=squarish',
        backgroundColor: AppColors.categoryClothing.withValues(alpha: 0.1),
        productCount: 1832,
        description: 'Clothing & Accessories',
        subcategories: [
          'Men\'s Wear',
          'Women\'s Wear',
          'Shoes',
          'Bags',
          'Jewelry',
          'Accessories',
        ],
      ),
      Category(
        id: 'home_living',
        name: 'Home & Living',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20home%20and%20furniture%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=40&height=40&seq=25&orientation=squarish',
        backgroundColor: AppColors.categoryHome.withValues(alpha: 0.1),
        productCount: 1245,
        description: 'Furniture & Decor',
        subcategories: [
          'Furniture',
          'Decor',
          'Bedding',
          'Kitchen',
          'Storage',
          'Lighting',
        ],
      ),
      Category(
        id: 'beauty',
        name: 'Beauty',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20beauty%20and%20cosmetics%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=40&height=40&seq=26&orientation=squarish',
        backgroundColor: AppColors.categoryBeauty.withValues(alpha: 0.1),
        productCount: 987,
        description: 'Cosmetics & Personal Care',
        subcategories: [
          'Skincare',
          'Makeup',
          'Hair Care',
          'Fragrances',
          'Tools',
          'Personal Care',
        ],
      ),
      Category(
        id: 'sports',
        name: 'Sports',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20sports%20equipment%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=40&height=40&seq=27&orientation=squarish',
        backgroundColor: AppColors.categorySports.withValues(alpha: 0.1),
        productCount: 654,
        description: 'Equipment & Accessories',
        subcategories: [
          'Fitness',
          'Outdoor',
          'Team Sports',
          'Water Sports',
          'Accessories',
          'Clothing',
        ],
      ),
    ];
  }

  static List<Category> getPopularCategories() {
    return [
      Category(
        id: 'electronics',
        name: 'Electronics',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20electronics%20and%20gadgets%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=48&height=48&seq=21&orientation=squarish',
        backgroundColor: AppColors.categoryElectronics.withValues(alpha: 0.1),
        productCount: 2156,
      ),
      Category(
        id: 'fashion',
        name: 'Fashion',
        iconUrl:
            'https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20fashion%20and%20clothing%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=48&height=48&seq=22&orientation=squarish',
        backgroundColor: AppColors.categoryClothing.withValues(alpha: 0.1),
        productCount: 1832,
      ),
    ];
  }
}
