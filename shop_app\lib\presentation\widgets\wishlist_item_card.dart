import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/product.dart';

class WishlistItemCard extends StatefulWidget {
  final Product product;
  final VoidCallback? onTap;
  final VoidCallback? onRemove;
  final VoidCallback? onAddToCart;

  const WishlistItemCard({
    super.key,
    required this.product,
    this.onTap,
    this.onRemove,
    this.onAddToCart,
  });

  @override
  State<WishlistItemCard> createState() => _WishlistItemCardState();
}

class _WishlistItemCardState extends State<WishlistItemCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _colorAnimation = ColorTween(
      begin: AppColors.cardBackground,
      end: AppColors.borderLight,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
    widget.onTap?.call();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              decoration: BoxDecoration(
                color: _colorAnimation.value,
                borderRadius: BorderRadius.circular(AppConstants.radiusCard),
                border: Border.all(
                  color: AppColors.border.withValues(alpha: 0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(
                      alpha: _isPressed ? 0.12 : 0.06,
                    ),
                    blurRadius: _isPressed ? 8 : 4,
                    offset: Offset(0, _isPressed ? 3 : 1),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                children: [
                  _buildProductImage(context, isTablet),
                  Expanded(
                    child: _buildProductInfo(context, isTablet),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProductImage(BuildContext context, bool isTablet) {
    final imageSize = isTablet ? 120.0 : 96.0;

    return Container(
      width: imageSize,
      height: imageSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(AppConstants.radiusCard),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(AppConstants.radiusCard),
        ),
        child: CachedNetworkImage(
          imageUrl: widget.product.imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: AppColors.borderLight,
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: AppColors.borderLight,
            child: Icon(
              Icons.image_not_supported,
              color: AppColors.textSecondary,
              size: isTablet ? 32 : 24,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProductInfo(BuildContext context, bool isTablet) {
    return Padding(
      padding: EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProductName(context, isTablet),
                    SizedBox(height: AppConstants.paddingSmall),
                    _buildRating(context, isTablet),
                    SizedBox(height: AppConstants.paddingSmall),
                    _buildPrice(context, isTablet),
                  ],
                ),
              ),
              _buildActionButtons(context, isTablet),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductName(BuildContext context, bool isTablet) {
    return Text(
      widget.product.name,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w500,
        fontSize: isTablet ? 16 : 14,
        color: AppColors.textPrimary,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildRating(BuildContext context, bool isTablet) {
    final starSize = isTablet ? 16.0 : 14.0;
    final textSize = isTablet ? 13.0 : 12.0;

    return Row(
      children: [
        ...List.generate(5, (index) {
          if (index < widget.product.rating.floor()) {
            return Icon(Icons.star, size: starSize, color: AppColors.rating);
          } else if (index < widget.product.rating) {
            return Icon(
              Icons.star_half,
              size: starSize,
              color: AppColors.rating,
            );
          } else {
            return Icon(
              Icons.star_border,
              size: starSize,
              color: AppColors.rating,
            );
          }
        }),
        SizedBox(width: AppConstants.paddingSmall),
        Text(
          widget.product.rating.toString(),
          style: TextStyle(
            fontSize: textSize,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildPrice(BuildContext context, bool isTablet) {
    final priceSize = isTablet ? 16.0 : 14.0;
    final originalPriceSize = isTablet ? 13.0 : 12.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.product.formattedPrice,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: priceSize,
            color: AppColors.textPrimary,
          ),
        ),
        if (widget.product.hasDiscount)
          Text(
            widget.product.formattedOriginalPrice,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              decoration: TextDecoration.lineThrough,
              color: AppColors.textSecondary,
              fontSize: originalPriceSize,
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isTablet) {
    final buttonSize = isTablet ? 40.0 : 32.0;
    final iconSize = isTablet ? 20.0 : 16.0;

    return Column(
      children: [
        // Remove button
        GestureDetector(
          onTap: widget.onRemove,
          child: Container(
            width: buttonSize,
            height: buttonSize,
            decoration: BoxDecoration(
              color: Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.delete_outline,
              size: iconSize,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        SizedBox(height: AppConstants.paddingSmall),
        // Add to cart button
        GestureDetector(
          onTap: widget.onAddToCart,
          child: Container(
            width: buttonSize,
            height: buttonSize,
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              size: iconSize,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
