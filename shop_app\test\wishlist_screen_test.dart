import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shop_app/presentation/screens/wishlist_screen.dart';
import 'package:shop_app/core/theme/app_theme.dart';

void main() {
  group('WishlistScreen Tests', () {
    testWidgets('WishlistScreen displays correctly with items', (WidgetTester tester) async {
      // Build the WishlistScreen widget
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WishlistScreen(),
        ),
      );

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify that the app bar is displayed
      expect(find.text('My Wishlist'), findsOneWidget);

      // Verify that wishlist items are displayed
      expect(find.text('4 items saved'), findsOneWidget);

      // Verify that product cards are displayed
      expect(find.text('Wireless Noise Cancelling Headphones'), findsOneWidget);
      expect(find.text('Smart Watch Series 7 - Health Monitor'), findsOneWidget);
      expect(find.text('Modern LED Desk Lamp with Wireless Charging'), findsOneWidget);
      expect(find.text('Anti-Theft Travel Backpack with USB Charging'), findsOneWidget);

      // Verify that prices are displayed
      expect(find.text('\$129.99'), findsOneWidget);
      expect(find.text('\$199.99'), findsOneWidget);
      expect(find.text('\$59.99'), findsOneWidget);
      expect(find.text('\$69.99'), findsOneWidget);
    });

    testWidgets('WishlistScreen remove functionality works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WishlistScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the first remove button
      final removeButtons = find.byIcon(Icons.delete_outline);
      expect(removeButtons, findsWidgets);

      await tester.tap(removeButtons.first);
      await tester.pumpAndSettle();

      // Verify that a snackbar is shown
      expect(find.text('Item removed from wishlist'), findsOneWidget);

      // Verify that the item count is updated
      expect(find.text('3 items saved'), findsOneWidget);
    });

    testWidgets('WishlistScreen add to cart functionality works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WishlistScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the first add to cart button
      final addToCartButtons = find.byIcon(Icons.shopping_cart_outlined);
      expect(addToCartButtons, findsWidgets);

      await tester.tap(addToCartButtons.first);
      await tester.pumpAndSettle();

      // Verify that a snackbar is shown
      expect(find.textContaining('added to cart'), findsOneWidget);
    });

    testWidgets('WishlistScreen clear all functionality works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WishlistScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the clear all button
      await tester.tap(find.text('Clear All'));
      await tester.pumpAndSettle();

      // Verify that a dialog is shown
      expect(find.text('Clear Wishlist'), findsOneWidget);
      expect(find.text('Are you sure you want to remove all items from your wishlist?'), findsOneWidget);

      // Tap the clear all button in the dialog
      await tester.tap(find.text('Clear All').last);
      await tester.pumpAndSettle();

      // Verify that the empty state is shown
      expect(find.text('Your Wishlist is Empty'), findsOneWidget);
      expect(find.text('Save items you love and want to buy later'), findsOneWidget);
      expect(find.text('Start Shopping'), findsOneWidget);
    });

    testWidgets('WishlistScreen responsive design works', (WidgetTester tester) async {
      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(800, 600));
      
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WishlistScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the screen renders correctly on tablet
      expect(find.text('My Wishlist'), findsOneWidget);
      expect(find.text('4 items saved'), findsOneWidget);

      // Reset to mobile size
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpAndSettle();

      // Verify that the screen still renders correctly on mobile
      expect(find.text('My Wishlist'), findsOneWidget);
      expect(find.text('4 items saved'), findsOneWidget);
    });
  });
}
