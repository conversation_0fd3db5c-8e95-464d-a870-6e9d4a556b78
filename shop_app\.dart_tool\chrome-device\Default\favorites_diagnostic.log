2025-06-17 07:36:02.974: [INFO] VisibilityChanged: 0
2025-06-17 07:36:02.989: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.990: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.990: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.990: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.991: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.991: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.991: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.992: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.992: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.993: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.993: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:02.997: [INFO] BookmarkModelLoaded, ids_reassigned: 0
2025-06-17 07:36:02.997: [INFO] OnDoneLoading sync enabled: 0
2025-06-17 07:36:03.001: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 0
2025-06-17 07:36:03.087: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 3
2025-06-17 07:36:03.088: [INFO] Primary account changed.
2025-06-17 07:36:03.088: [INFO] OnPrimaryAccountChanged PrimaryAccountChangeEvent::Type::kSet
2025-06-17 07:36:03.090: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 3
2025-06-17 07:36:03.159: [INFO] RecordSyncInitDuration has_sync_been_enabled: 0 state: 3
2025-06-17 07:36:09.736: [INFO] Sync initialization duration: 6506ms
