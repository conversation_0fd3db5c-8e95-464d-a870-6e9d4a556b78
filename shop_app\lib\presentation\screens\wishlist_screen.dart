import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/product.dart';
import '../widgets/wishlist_item_card.dart';

class WishlistScreen extends StatefulWidget {
  const WishlistScreen({super.key});

  @override
  State<WishlistScreen> createState() => _WishlistScreenState();
}

class _WishlistScreenState extends State<WishlistScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<Product> _wishlistItems = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadWishlistItems();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadWishlistItems() {
    // Sample wishlist data matching the HTML design
    _wishlistItems = [
      Product(
        id: '1',
        name: 'Wireless Noise Cancelling Headphones',
        description:
            'Premium wireless headphones with active noise cancellation',
        price: 129.99,
        imageUrl:
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
        rating: 4.5,
        reviewCount: 128,
        category: 'Electronics',
        isFavorite: true,
      ),
      Product(
        id: '2',
        name: 'Smart Watch Series 7 - Health Monitor',
        description: 'Advanced smartwatch with comprehensive health monitoring',
        price: 199.99,
        imageUrl:
            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
        rating: 4.0,
        reviewCount: 89,
        category: 'Electronics',
        isFavorite: true,
      ),
      Product(
        id: '3',
        name: 'Modern LED Desk Lamp with Wireless Charging',
        description: 'Sleek desk lamp with built-in wireless charging pad',
        price: 59.99,
        imageUrl:
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop',
        rating: 5.0,
        reviewCount: 45,
        category: 'Home',
        isFavorite: true,
      ),
      Product(
        id: '4',
        name: 'Anti-Theft Travel Backpack with USB Charging',
        description: 'Secure travel backpack with USB charging port',
        price: 69.99,
        imageUrl:
            'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=400&h=400&fit=crop',
        rating: 4.1,
        reviewCount: 67,
        category: 'Travel',
        isFavorite: true,
      ),
    ];
  }

  void _removeFromWishlist(String productId) {
    setState(() {
      _wishlistItems.removeWhere((item) => item.id == productId);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Item removed from wishlist'),
        backgroundColor: AppColors.textSecondary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusButton),
        ),
        action: SnackBarAction(
          label: 'Undo',
          textColor: AppColors.primary,
          onPressed: () {
            // Implement undo functionality
          },
        ),
      ),
    );
  }

  void _addToCart(Product product) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${product.name} added to cart'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusButton),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(context, isTablet),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: _wishlistItems.isEmpty
            ? _buildEmptyState(context, isTablet)
            : _buildWishlistContent(context, isTablet),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isTablet) {
    return AppBar(
      backgroundColor: AppColors.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: AppColors.textPrimary,
          size: isTablet ? 24 : 20,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'My Wishlist',
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 24 : 20,
          color: AppColors.textPrimary,
        ),
      ),
      actions: [
        if (_wishlistItems.isNotEmpty)
          TextButton(
            onPressed: () {
              _showClearAllDialog(context);
            },
            child: Text(
              'Clear All',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: isTablet ? 16 : 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        SizedBox(width: AppConstants.paddingSmall),
      ],
    );
  }

  Widget _buildWishlistContent(BuildContext context, bool isTablet) {
    return Column(
      children: [
        _buildWishlistHeader(context, isTablet),
        Expanded(child: _buildWishlistList(context, isTablet)),
      ],
    );
  }

  Widget _buildWishlistHeader(BuildContext context, bool isTablet) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Row(
        children: [
          Text(
            '${_wishlistItems.length} items saved',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
              fontSize: isTablet ? 16 : 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWishlistList(BuildContext context, bool isTablet) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
      itemCount: _wishlistItems.length,
      separatorBuilder: (context, index) =>
          SizedBox(height: AppConstants.paddingMedium),
      itemBuilder: (context, index) {
        final product = _wishlistItems[index];
        return WishlistItemCard(
          product: product,
          onRemove: () => _removeFromWishlist(product.id),
          onAddToCart: () => _addToCart(product),
          onTap: () {
            // Navigate to product detail
          },
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isTablet) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: isTablet ? 120 : 96,
              height: isTablet ? 120 : 96,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.favorite_border,
                size: isTablet ? 60 : 48,
                color: AppColors.primary,
              ),
            ),
            SizedBox(height: AppConstants.paddingLarge),
            Text(
              'Your Wishlist is Empty',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: isTablet ? 24 : 20,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Save items you love and want to buy later',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
                fontSize: isTablet ? 16 : 14,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppConstants.paddingLarge),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingLarge,
                  vertical: AppConstants.paddingMedium,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusButton,
                  ),
                ),
              ),
              child: Text(
                'Start Shopping',
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusCard),
          ),
          title: const Text('Clear Wishlist'),
          content: const Text(
            'Are you sure you want to remove all items from your wishlist?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _wishlistItems.clear();
                });
                Navigator.of(context).pop();
              },
              child: Text(
                'Clear All',
                style: TextStyle(color: AppColors.primary),
              ),
            ),
          ],
        );
      },
    );
  }
}
