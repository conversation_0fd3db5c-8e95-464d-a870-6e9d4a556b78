{"logTime": "0617/073604", "correlationVector":"EF6wDUCjWyPE23pEx6LRK2.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800001k"}}
{"logTime": "0617/073604", "correlationVector":"EF6wDUCjWyPE23pEx6LRK2.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Encryption Keys", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073604", "correlationVector":"0IHch9sp/e4VjKW4DF8Aht","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"", "migrationStage":"", "server":""}}
{"logTime": "0617/073604", "correlationVector":"0IHch9sp/e4VjKW4DF8Aht.0","action":"EncryptionKeysFetcher.DownloadKeys:", "result":"Success", "context":Key count: 1, Last key timestamp: 2025-06-04T21:53:12Z}
{"logTime": "0617/073604", "correlationVector":"0IHch9sp/e4VjKW4DF8Aht.1","action":"EncryptionKeysManager::SetEncryptionKeys:", "result":"Success", "context":Key names[1]:[WSRz5BpWCN7ADUkNK+2IEi5DwfqS4n1BpPEychhxPiFiE28qIb4Oecor+LR+BKoeUE6cIfQEpviCQYinMNXDlw==]}
{"logTime": "0617/073604", "correlationVector":"0IHch9sp/e4VjKW4DF8Aht.2","action":"EncryptionKeysManager::SetEncryptionKeysWithTimestamps:", "result":"Success", "context":Key timestamps[1]:[2025-06-04T21:53:12Z]}
{"logTime": "0617/073604", "correlationVector":"EF6wDUCjWyPE23pEx6LRK2","action":"Initial GetUpdates", "result":"", "context":Reason: NEW_CLIENT. cV=EF6wDUCjWyPE23pEx6LRK2}
{"logTime": "0617/073604", "correlationVector":"EF6wDUCjWyPE23pEx6LRK2.3","action":"GetUpdates Response", "result":"Success", "context":Received 1 update(s). cV=EF6wDUCjWyPE23pEx6LRK2.0;server=akswtt00800001k;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073604", "correlationVector":"r8aSVgOfGT9X15LoGG0ukz","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=r8aSVgOfGT9X15LoGG0ukz}
{"logTime": "0617/073605", "correlationVector":"r8aSVgOfGT9X15LoGG0ukz.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000033"}}
{"logTime": "0617/073605", "correlationVector":"r8aSVgOfGT9X15LoGG0ukz.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"45", "total":"45"}}
{"logTime": "0617/073605", "correlationVector":"r8aSVgOfGT9X15LoGG0ukz.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"9", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"15", "total":"15"}}
{"logTime": "0617/073605", "correlationVector":"r8aSVgOfGT9X15LoGG0ukz.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0617/073605", "correlationVector":"r8aSVgOfGT9X15LoGG0ukz.5","action":"GetUpdates Response", "result":"Success", "context":Received 64 update(s). cV=r8aSVgOfGT9X15LoGG0ukz.0;server=akswtt008000033;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073605", "correlationVector":"KMz+Jj6aI4Z7928j3KoxEW","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=KMz+Jj6aI4Z7928j3KoxEW}
{"logTime": "0617/073605", "correlationVector":"KMz+Jj6aI4Z7928j3KoxEW.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800001v"}}
{"logTime": "0617/073605", "correlationVector":"KMz+Jj6aI4Z7928j3KoxEW.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Bookmarks", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "0617/073605", "correlationVector":"KMz+Jj6aI4Z7928j3KoxEW.3","action":"GetUpdates Response", "result":"Success", "context":Received 5 update(s). cV=KMz+Jj6aI4Z7928j3KoxEW.0;server=akswtt00800001v;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073605", "correlationVector":"suGaoE7w7Ev+IYqzCTvBcc","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=suGaoE7w7Ev+IYqzCTvBcc}
{"logTime": "0617/073606", "correlationVector":"suGaoE7w7Ev+IYqzCTvBcc.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000046"}}
{"logTime": "0617/073606", "correlationVector":"suGaoE7w7Ev+IYqzCTvBcc.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073606", "correlationVector":"suGaoE7w7Ev+IYqzCTvBcc.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"243", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"249", "total":"249"}}
{"logTime": "0617/073606", "correlationVector":"suGaoE7w7Ev+IYqzCTvBcc.4","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=suGaoE7w7Ev+IYqzCTvBcc.0;server=akswtt008000046;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0617/073606", "correlationVector":"lHIZdozogrAlkOlhJpp4C7","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=lHIZdozogrAlkOlhJpp4C7}
{"logTime": "0617/073607", "correlationVector":"lHIZdozogrAlkOlhJpp4C7.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800006s"}}
{"logTime": "0617/073607", "correlationVector":"lHIZdozogrAlkOlhJpp4C7.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073607", "correlationVector":"lHIZdozogrAlkOlhJpp4C7.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"246", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"246", "total":"246"}}
{"logTime": "0617/073607", "correlationVector":"lHIZdozogrAlkOlhJpp4C7.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extension settings", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073607", "correlationVector":"lHIZdozogrAlkOlhJpp4C7.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Web Apps", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0617/073607", "correlationVector":"lHIZdozogrAlkOlhJpp4C7.6","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=lHIZdozogrAlkOlhJpp4C7.0;server=akswtt00800006s;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0617/073607", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=t2oCx7vWQ8s+hPOP3k/NKn}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000024"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"6", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"6", "total":"6"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"51", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"55", "total":"55"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"184", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"184", "total":"184"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History Delete Directives", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Web Apps", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0617/073608", "correlationVector":"t2oCx7vWQ8s+hPOP3k/NKn.8","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=t2oCx7vWQ8s+hPOP3k/NKn.0;server=akswtt008000024;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=TdTrzhzkJWTFbZPXdqzkZY}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000030"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"21", "total":"21"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"213", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"213", "total":"213"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extension settings", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0617/073608", "correlationVector":"TdTrzhzkJWTFbZPXdqzkZY.8","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=TdTrzhzkJWTFbZPXdqzkZY.0;server=akswtt008000030;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0617/073608", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=DXgZV7Oupw2B9NpHZ7Yu3Y}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800006q"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"13", "total":"13"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"213", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"213", "total":"213"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extension settings", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"18", "total":"18"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History Delete Directives", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073609", "correlationVector":"DXgZV7Oupw2B9NpHZ7Yu3Y.8","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=DXgZV7Oupw2B9NpHZ7Yu3Y.0;server=akswtt00800006q;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=n3UUuT9uCZ/5vQ6q5lMVZ8}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800003c"}}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"55", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"63", "total":"63"}}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extension settings", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0617/073609", "correlationVector":"n3UUuT9uCZ/5vQ6q5lMVZ8.6","action":"GetUpdates Response", "result":"Success", "context":Received 68 update(s). cV=n3UUuT9uCZ/5vQ6q5lMVZ8.0;server=akswtt00800003c;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073609", "correlationVector":"Hb/9epLcxbG3XwsFZzwyRa","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=Hb/9epLcxbG3XwsFZzwyRa}
{"logTime": "0617/073609", "correlationVector":"Hb/9epLcxbG3XwsFZzwyRa.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt008000004"}}
{"logTime": "0617/073609", "correlationVector":"Hb/9epLcxbG3XwsFZzwyRa.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"3", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0617/073609", "correlationVector":"Hb/9epLcxbG3XwsFZzwyRa.3","action":"GetUpdates Response", "result":"Success", "context":Received 3 update(s). cV=Hb/9epLcxbG3XwsFZzwyRa.0;server=akswtt008000004;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073609", "correlationVector":"XdpCDxXhf8zzZzzUJniLYp","action":"Normal GetUpdate request", "result":"", "context":cV=XdpCDxXhf8zzZzzUJniLYp
Nudged types: Sessions, Device Info, User Consents
Refresh requested types: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys}
{"logTime": "0617/073610", "correlationVector":"XdpCDxXhf8zzZzzUJniLYp.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800006s"}}
{"logTime": "0617/073610", "correlationVector":"XdpCDxXhf8zzZzzUJniLYp.2","action":"GetUpdates Response", "result":"Success", "context":Received 0 update(s). cV=XdpCDxXhf8zzZzzUJniLYp.0;server=akswtt00800006s;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK","action":"DoCompareDataConsistency requsted types: ", "result":"Bookmarks, Preferences, Passwords, Extensions, Extension settings, History Delete Directives, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Wallet, Encryption Keys"}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.0","action":"CompareDataConsistency result: ", "result":"Compare result: Encryption Keys is consistent. local entities count is: 1 local entities hash is: TALkIS+sSGDtO6px7gVawVmyCgY="}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.1","action":"CompareDataConsistency result: ", "result":"Compare result: Bookmarks is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.2","action":"CompareDataConsistency result: ", "result":"Compare result: Preferences is consistent. local entities count is: 45 local entities hash is: 7DosR5ZIDZjwgHvu+3mh7TLasJQ="}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.3","action":"CompareDataConsistency result: ", "result":"Compare result: Extensions is consistent. local entities count is: 3 local entities hash is: A6SLraA7vpIxIhFk4WrQWGUGs1A="}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.4","action":"CompareDataConsistency result: ", "result":"Compare result: Extension settings is consistent. local entities count is: 25 local entities hash is: 43kzTXljJgSDSucmhAOX0q2nTto="}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.5","action":"CompareDataConsistency result: ", "result":"Compare result: History Delete Directives is inconsistent. server entities count is: 3 server entities hash is: dypkZVZbntthHAqpNIIkWon9FwY= local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.6","action":"CompareDataConsistency result: ", "result":"Server did not send this Send Tab To Self local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.7","action":"CompareDataConsistency result: ", "result":"Compare result: Web Apps is consistent. local entities count is: 3 local entities hash is: CjbK3Cn/Syyg42XZec+R/0IU6/k="}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.8","action":"CompareDataConsistency result: ", "result":"Server did not send this History local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.9","action":"CompareDataConsistency result: ", "result":"Compare result: Saved Tab Group is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.10","action":"CompareDataConsistency result: ", "result":"Server did not send this WebAuthn Credentials local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.11","action":"CompareDataConsistency result: ", "result":"Compare result: Collection is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.12","action":"CompareDataConsistency result: ", "result":"Compare result: Edge E Drop is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.13","action":"CompareDataConsistency result: ", "result":"Compare result: Edge Wallet is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0617/073610", "correlationVector":"BdbW90oZD0f/tBqtq/TkmK.14","action":"CompareDataConsistency result: ", "result":"Compare result: Passwords is consistent. local entities count is: 6 local entities hash is: FihOZNL0q5CpYyG+VGvlN3iF71E="}
{"logTime": "0617/073610", "correlationVector":"PXW01dSM+iUc+0fd7DRcVd","action":"Commit Request", "result":"", "context":Item count: 5
Contributing types: Sessions, Device Info, User Consents}
{"logTime": "0617/073610", "correlationVector":"PXW01dSM+iUc+0fd7DRcVd.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00800002e"}}
{"logTime": "0617/073610", "correlationVector":"PXW01dSM+iUc+0fd7DRcVd.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=PXW01dSM+iUc+0fd7DRcVd.0;server=akswtt00800002e;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0617/073610", "correlationVector":"","action":"DIAGNOSTIC_REQUEST|v1/diagnosticData/Diagnostic.SendCheckResult()|SUCCESS", "result":""}
