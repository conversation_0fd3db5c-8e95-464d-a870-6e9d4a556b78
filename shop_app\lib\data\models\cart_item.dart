import 'product.dart';

class CartItem {
  final String id;
  final Product product;
  int quantity;

  CartItem({
    required this.id,
    required this.product,
    this.quantity = 1,
  });

  CartItem copyWith({
    String? id,
    Product? product,
    int? quantity,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
    );
  }

  double get totalPrice => product.price * quantity;
  
  String get formattedTotalPrice => '\$${totalPrice.toStringAsFixed(2)}';
  
  String get quantityText => '$quantity × ${product.formattedPrice}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.id == id &&
        other.product.id == product.id &&
        other.quantity == quantity;
  }

  @override
  int get hashCode => id.hashCode ^ product.id.hashCode ^ quantity.hashCode;
}

class Cart {
  final List<CartItem> items;

  Cart({this.items = const []});

  Cart copyWith({List<CartItem>? items}) {
    return Cart(items: items ?? this.items);
  }

  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);
  
  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);
  
  double get total => subtotal; // Add tax/shipping logic here if needed
  
  String get formattedSubtotal => '\$${subtotal.toStringAsFixed(2)}';
  
  String get formattedTotal => '\$${total.toStringAsFixed(2)}';
  
  bool get isEmpty => items.isEmpty;
  
  bool get isNotEmpty => items.isNotEmpty;

  CartItem? findItem(String productId) {
    try {
      return items.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  Cart addItem(Product product, {int quantity = 1}) {
    final existingItem = findItem(product.id);
    
    if (existingItem != null) {
      // Update quantity of existing item
      final updatedItems = items.map((item) {
        if (item.product.id == product.id) {
          return item.copyWith(quantity: item.quantity + quantity);
        }
        return item;
      }).toList();
      
      return copyWith(items: updatedItems);
    } else {
      // Add new item
      final newItem = CartItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        product: product,
        quantity: quantity,
      );
      
      return copyWith(items: [...items, newItem]);
    }
  }

  Cart removeItem(String itemId) {
    final updatedItems = items.where((item) => item.id != itemId).toList();
    return copyWith(items: updatedItems);
  }

  Cart updateItemQuantity(String itemId, int newQuantity) {
    if (newQuantity <= 0) {
      return removeItem(itemId);
    }
    
    final updatedItems = items.map((item) {
      if (item.id == itemId) {
        return item.copyWith(quantity: newQuantity);
      }
      return item;
    }).toList();
    
    return copyWith(items: updatedItems);
  }

  Cart increaseItemQuantity(String itemId) {
    final item = items.firstWhere((item) => item.id == itemId);
    return updateItemQuantity(itemId, item.quantity + 1);
  }

  Cart decreaseItemQuantity(String itemId) {
    final item = items.firstWhere((item) => item.id == itemId);
    return updateItemQuantity(itemId, item.quantity - 1);
  }

  Cart clear() {
    return copyWith(items: []);
  }
}
