import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/category.dart';
import 'subcategory_item.dart';

class ExpandableCategoryCard extends StatefulWidget {
  final Category category;
  final VoidCallback? onCategoryTap;
  final Function(String)? onSubcategoryTap;

  const ExpandableCategoryCard({
    super.key,
    required this.category,
    this.onCategoryTap,
    this.onSubcategoryTap,
  });

  @override
  State<ExpandableCategoryCard> createState() => _ExpandableCategoryCardState();
}

class _ExpandableCategoryCardState extends State<ExpandableCategoryCard>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.animationMedium,
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.5).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? AppColors.darkCard
            : Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusCard),
        border: Border.all(
          color: AppColors.border.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.secondary.withValues(alpha: 0.12),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: AppColors.shadowLight.withValues(alpha: 0.06),
            blurRadius: 6,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // Category Header
          GestureDetector(
            onTap: _toggleExpansion,
            child: Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Row(
                children: [
                  // Category Icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          widget.category.backgroundColor.withValues(
                            alpha: 0.12,
                          ),
                          widget.category.backgroundColor.withValues(
                            alpha: 0.06,
                          ),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusLarge,
                      ),
                      border: Border.all(
                        color: widget.category.backgroundColor.withValues(
                          alpha: 0.25,
                        ),
                        width: 1.2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: widget.category.backgroundColor.withValues(
                            alpha: 0.15,
                          ),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(
                        AppConstants.radiusLarge,
                      ),
                      child: CachedNetworkImage(
                        imageUrl: widget.category.iconUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: widget.category.backgroundColor.withValues(
                            alpha: 0.1,
                          ),
                          child: Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getCategoryColor(widget.category.id),
                              ),
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: widget.category.backgroundColor.withValues(
                            alpha: 0.1,
                          ),
                          child: Icon(
                            _getCategoryIcon(widget.category.id),
                            color: _getCategoryColor(widget.category.id),
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  // Category Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.category.name,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        if (widget.category.description != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            widget.category.description!,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w400,
                                ),
                          ),
                        ],
                        const SizedBox(height: 4),
                        Text(
                          '${widget.category.productCount.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} items',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppColors.secondary.withValues(
                                  alpha: 0.9,
                                ),
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ],
                    ),
                  ),
                  // Expand Arrow
                  AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotationAnimation.value * 3.14159,
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: AppColors.primary.withValues(alpha: 0.8),
                          size: 24,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          // Subcategories
          if (_isExpanded && widget.category.subcategories != null)
            Container(
              padding: const EdgeInsets.only(
                left: AppConstants.paddingMedium,
                right: AppConstants.paddingMedium,
                bottom: AppConstants.paddingMedium,
                top: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: AppColors.border.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
              ),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: AppConstants.paddingSmall,
                  mainAxisSpacing: AppConstants.paddingSmall,
                  childAspectRatio: 3.5,
                ),
                itemCount: widget.category.subcategories!.length,
                itemBuilder: (context, index) {
                  final subcategory = widget.category.subcategories![index];
                  return SubcategoryItem(
                    name: subcategory,
                    onTap: () => widget.onSubcategoryTap?.call(subcategory),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case 'electronics':
        return Icons.devices;
      case 'fashion':
      case 'clothing':
        return Icons.checkroom;
      case 'home':
      case 'home_living':
        return Icons.home;
      case 'beauty':
        return Icons.face;
      case 'sports':
        return Icons.sports;
      case 'toys':
        return Icons.toys;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String categoryId) {
    switch (categoryId) {
      case 'electronics':
        return AppColors.categoryElectronics;
      case 'fashion':
      case 'clothing':
        return AppColors.categoryClothing;
      case 'home':
      case 'home_living':
        return AppColors.categoryHome;
      case 'beauty':
        return AppColors.categoryBeauty;
      case 'sports':
        return AppColors.categorySports;
      case 'toys':
        return AppColors.categoryToys;
      default:
        return AppColors.primary;
    }
  }
}
